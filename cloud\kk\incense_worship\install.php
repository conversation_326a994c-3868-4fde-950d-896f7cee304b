<?php
// 网络烧香拜佛系统一键安装程序
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查是否已经安装
if (file_exists('config/database.php') && file_exists('install.lock')) {
    die('系统已经安装完成！如需重新安装，请删除 install.lock 文件。');
}

// 优先从POST获取step
$step = isset($_POST['step']) ? (int)$_POST['step'] : (isset($_GET['step']) ? (int)$_GET['step'] : 1);
$error = '';
$success = '';

// 处理安装步骤
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 2:
            // 环境检测 - 重定向到下一步
            header('Location: ?step=3');
            exit();
            break;
            
        case 3:
            // 数据库配置 - 调试信息
            $debug = "POST数据: host=" . ($_POST['host'] ?? 'NULL') . 
                    ", dbname=" . ($_POST['dbname'] ?? 'NULL') . 
                    ", username=" . ($_POST['username'] ?? 'NULL');
            
            $host = isset($_POST['host']) ? trim($_POST['host']) : '';
            $dbname = isset($_POST['dbname']) ? trim($_POST['dbname']) : '';
            $username = isset($_POST['username']) ? trim($_POST['username']) : '';
            $password = isset($_POST['password']) ? $_POST['password'] : '';
            $site_name = isset($_POST['site_name']) ? trim($_POST['site_name']) : '';
            
            if (empty($host) || empty($dbname) || empty($username)) {
                $error = '请填写完整的数据库信息。调试信息：' . $debug;
            } else {
                // 测试数据库连接
                try {
                    $dsn = "mysql:host=$host;charset=utf8mb4";
                    $pdo = new PDO($dsn, $username, $password, [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    // 创建数据库（如果不存在）
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$dbname`");
                    
                    // 读取并执行SQL文件
                    $sqlContent = file_get_contents('database/database.sql');
                    if ($sqlContent === false) {
                        throw new Exception('无法读取数据库文件');
                    }
                    
                    // 替换数据库名
                    $sqlContent = str_replace('incense_worship', $dbname, $sqlContent);
                    
                    // 按分号分割语句
                    $statements = explode(';', $sqlContent);
                    
                    foreach ($statements as $statement) {
                        $statement = trim($statement);
                        if (!empty($statement) && !preg_match('/^(--|#)/', $statement)) {
                            try {
                                $pdo->exec($statement);
                            } catch (PDOException $e) {
                                // 忽略一些不重要的错误，如表已存在等
                                if (strpos($e->getMessage(), 'already exists') === false && 
                                    strpos($e->getMessage(), 'Duplicate entry') === false &&
                                    strpos($e->getMessage(), 'Duplicate key name') === false) {
                                    throw $e;
                                }
                            }
                        }
                    }
                    
                    // 验证表是否创建成功
                    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                    if (!in_array('users', $tables)) {
                        throw new Exception("users表创建失败，请检查SQL文件");
                    }
                    
                    // 生成配置文件
                    $configContent = generateConfigFile($host, $dbname, $username, $password, $site_name);
                    if (!file_put_contents('config/database.php', $configContent)) {
                        throw new Exception('无法写入配置文件');
                    }
                    
                    // 设置成功状态并直接进入步骤4
                    $step = 4;
                    $success = '数据库安装成功！现在请创建管理员账户。';
                    
                } catch (Exception $e) {
                    $error = '数据库连接失败：' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // 管理员账户创建
            $admin_username = isset($_POST['admin_username']) ? trim($_POST['admin_username']) : '';
            $admin_email = isset($_POST['admin_email']) ? trim($_POST['admin_email']) : '';
            $admin_password = isset($_POST['admin_password']) ? $_POST['admin_password'] : '';
            $admin_nickname = isset($_POST['admin_nickname']) ? trim($_POST['admin_nickname']) : '';
            
            if (empty($admin_username) || empty($admin_email) || empty($admin_password)) {
                $error = '请填写完整的管理员信息';
            } else {
                try {
                    include 'config/database.php';
                    include 'includes/auth.php';
                    
                    $auth = new Auth();
                    $result = $auth->register($admin_username, $admin_email, $admin_password, $admin_nickname);
                    
                    if ($result['success']) {
                        // 创建安装锁文件
                        file_put_contents('install.lock', date('Y-m-d H:i:s'));
                        
                        // 创建必要目录
                        createDirectories();
                        
                        $step = 5;
                        $success = '管理员账户创建成功！系统安装完成！';
                    } else {
                        $error = $result['message'];
                    }
                } catch (Exception $e) {
                    $error = '创建管理员账户失败：' . $e->getMessage();
                }
            }
            break;
    }
}

// 生成配置文件内容
function generateConfigFile($host, $dbname, $username, $password, $siteName) {
    return "<?php
// 数据库配置文件
class Database {
    private \$host = '$host';
    private \$db_name = '$dbname';
    private \$username = '$username';
    private \$password = '$password';
    private \$conn;
    
    public function connect() {
        \$this->conn = null;
        
        try {
            \$this->conn = new PDO(
                'mysql:host=' . \$this->host . ';dbname=' . \$this->db_name . ';charset=utf8mb4',
                \$this->username,
                \$this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                )
            );
        } catch(PDOException \$e) {
            echo 'Connection Error: ' . \$e->getMessage();
        }
        
        return \$this->conn;
    }
}

// 全局数据库连接函数
function getDBConnection() {
    \$database = new Database();
    return \$database->connect();
}

// 系统配置
define('SITE_NAME', '$siteName');
define('SITE_URL', 'http://localhost/incense_worship');
define('UPLOAD_PATH', 'uploads/');
define('DEFAULT_AVATAR', 'images/default_avatar.jpg');

// 会话配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
session_start();

// 通用函数
function isLoggedIn() {
    return isset(\$_SESSION['user_id']) && !empty(\$_SESSION['user_id']);
}

function getCurrentUserId() {
    return isset(\$_SESSION['user_id']) ? \$_SESSION['user_id'] : 0;
}

function redirectTo(\$url) {
    header(\"Location: \$url\");
    exit();
}

function sanitizeInput(\$data) {
    return htmlspecialchars(strip_tags(trim(\$data)));
}

function formatDateTime(\$datetime) {
    return date('Y-m-d H:i:s', strtotime(\$datetime));
}

function timeAgo(\$datetime) {
    \$time = time() - strtotime(\$datetime);
    
    if (\$time < 60) return '刚刚';
    if (\$time < 3600) return floor(\$time/60) . '分钟前';
    if (\$time < 86400) return floor(\$time/3600) . '小时前';
    if (\$time < 2592000) return floor(\$time/86400) . '天前';
    if (\$time < 31536000) return floor(\$time/2592000) . '个月前';
    return floor(\$time/31536000) . '年前';
}
?>";
}

// 创建必要目录
function createDirectories() {
    $dirs = ['uploads', 'images', 'images/buddhas', 'images/incense'];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

// 环境检测
function checkEnvironment() {
    $checks = [
        'PHP版本 >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO扩展' => extension_loaded('pdo'),
        'PDO MySQL' => extension_loaded('pdo_mysql'),
        'JSON扩展' => extension_loaded('json'),
        '文件写入权限' => is_writable('.'),
        'config目录权限' => is_writable('config') || is_writable('.'),
        'database目录' => is_dir('database') && is_readable('database/database.sql')
    ];
    return $checks;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络烧香拜佛系统 - 安装向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .install-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 5px 10px;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }
        
        .step.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .step.completed {
            background: linear-gradient(45deg, #00b894, #00a085);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2d3436;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
        }
        
        .alert-error {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
        }
        
        .check-list {
            list-style: none;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .check-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .check-list li:last-child {
            border-bottom: none;
        }
        
        .status-ok {
            color: #00b894;
            font-weight: bold;
        }
        
        .status-error {
            color: #d63031;
            font-weight: bold;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mb-20 {
            margin-bottom: 20px;
        }
        
        .mt-20 {
            margin-top: 20px;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .welcome-content {
            text-align: center;
            padding: 20px;
        }
        
        .welcome-content h2 {
            color: #2d3436;
            margin-bottom: 15px;
        }
        
        .welcome-content p {
            color: #636e72;
            line-height: 1.6;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>网络烧香拜佛系统</h1>
            <p>安装向导</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : ''; ?>">1</div>
            <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : ''; ?>">2</div>
            <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : ''; ?>">3</div>
            <div class="step <?php echo $step >= 4 ? ($step == 4 ? 'active' : 'completed') : ''; ?>">4</div>
            <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
        </div>

        <!-- 错误和成功提示 -->
        <?php if ($error): ?>
        <div class="alert alert-error">
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- 步骤1：欢迎页面 -->
        <?php if ($step == 1): ?>
        <div class="welcome-content">
            <h2>欢迎使用网络烧香拜佛系统</h2>
            <p>这是一个基于PHP+MySQL的传统文化网络礼佛平台</p>
            <p>让用户可以在线烧香、拜佛、许愿，体验虔诚的佛教文化</p>
            <p>安装向导将引导您完成系统的安装配置</p>
            
            <div class="mt-20">
                <h3>系统特色功能：</h3>
                <ul style="text-align: left; display: inline-block; margin: 20px 0;">
                    <li>🙏 用户注册登录系统</li>
                    <li>🔥 虚拟烧香功能</li>
                    <li>📿 在线拜佛礼敬</li>
                    <li>⭐ 诚心许愿功能</li>
                    <li>💰 功德积分系统</li>
                    <li>📱 响应式界面设计</li>
                </ul>
            </div>
        </div>
        
        <div class="buttons">
            <a href="?step=2" class="btn">开始安装</a>
        </div>
        <?php endif; ?>

        <!-- 步骤2：环境检测 -->
        <?php if ($step == 2): ?>
        <h2 class="text-center mb-20">环境检测</h2>
        
        <ul class="check-list">
            <?php
            $checks = checkEnvironment();
            $allOk = true;
            foreach ($checks as $item => $status):
                if (!$status) $allOk = false;
            ?>
            <li>
                <span><?php echo $item; ?></span>
                <span class="<?php echo $status ? 'status-ok' : 'status-error'; ?>">
                    <?php echo $status ? '✓ 通过' : '✗ 失败'; ?>
                </span>
            </li>
            <?php endforeach; ?>
        </ul>
        
        <?php if (!$allOk): ?>
        <div class="alert alert-error">
            环境检测未通过！请解决上述问题后重新检测。
        </div>
        <?php endif; ?>
        
        <div class="buttons">
            <a href="?step=1" class="btn btn-secondary">上一步</a>
            <?php if ($allOk): ?>
            <form method="POST" style="display: inline;">
                <button type="submit" class="btn">下一步</button>
            </form>
            <?php else: ?>
            <a href="?step=2" class="btn">重新检测</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- 步骤3：数据库配置 -->
        <?php if ($step == 3): ?>
        <h2 class="text-center mb-20">数据库配置</h2>
        
        <form method="POST">
            <input type="hidden" name="step" value="3">
            <div class="form-group">
                <label>数据库主机：</label>
                <input type="text" name="host" value="<?php echo isset($_POST['host']) ? htmlspecialchars($_POST['host']) : 'localhost'; ?>" required>
            </div>
            
            <div class="form-group">
                <label>数据库名：</label>
                <input type="text" name="dbname" value="<?php echo isset($_POST['dbname']) ? htmlspecialchars($_POST['dbname']) : 'incense_worship'; ?>" required>
            </div>
            
            <div class="form-group">
                <label>数据库用户名：</label>
                <input type="text" name="username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : 'root'; ?>" required>
            </div>
            
            <div class="form-group">
                <label>数据库密码：</label>
                <input type="password" name="password" value="<?php echo isset($_POST['password']) ? htmlspecialchars($_POST['password']) : ''; ?>">
            </div>
            
            <div class="form-group">
                <label>网站名称：</label>
                <input type="text" name="site_name" value="<?php echo isset($_POST['site_name']) ? htmlspecialchars($_POST['site_name']) : '网络烧香拜佛'; ?>" required>
            </div>
            
            <div class="buttons">
                <a href="?step=2" class="btn btn-secondary">上一步</a>
                <button type="submit" class="btn">安装数据库</button>
            </div>
        </form>
        <?php endif; ?>

        <!-- 步骤4：创建管理员账户 -->
        <?php if ($step == 4): ?>
        <h2 class="text-center mb-20">创建管理员账户</h2>
        
        <form method="POST">
            <input type="hidden" name="step" value="4">
            <div class="form-group">
                <label>管理员用户名：</label>
                <input type="text" name="admin_username" value="<?php echo isset($_POST['admin_username']) ? htmlspecialchars($_POST['admin_username']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label>管理员邮箱：</label>
                <input type="email" name="admin_email" value="<?php echo isset($_POST['admin_email']) ? htmlspecialchars($_POST['admin_email']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label>管理员密码：</label>
                <input type="password" name="admin_password" required>
            </div>
            
            <div class="form-group">
                <label>管理员昵称：</label>
                <input type="text" name="admin_nickname" value="<?php echo isset($_POST['admin_nickname']) ? htmlspecialchars($_POST['admin_nickname']) : '系统管理员'; ?>">
            </div>
            
            <div class="buttons">
                <a href="?step=3" class="btn btn-secondary">上一步</a>
                <button type="submit" class="btn">创建账户</button>
            </div>
        </form>
        <?php endif; ?>

        <!-- 步骤5：安装完成 -->
        <?php if ($step == 5): ?>
        <div class="welcome-content">
            <h2>🎉 安装完成！</h2>
            <p>恭喜您！网络烧香拜佛系统已成功安装。</p>
            
            <div class="mt-20">
                <h3>安装信息：</h3>
                <ul style="text-align: left; display: inline-block; margin: 20px 0;">
                    <li>✅ 数据库安装完成</li>
                    <li>✅ 管理员账户创建成功</li>
                    <li>✅ 系统目录创建完成</li>
                    <li>✅ 配置文件生成成功</li>
                </ul>
            </div>
            
            <div class="alert alert-success">
                <strong>重要提示：</strong><br>
                1. 请删除 install.php 文件以确保安全<br>
                2. 建议修改数据库密码并定期备份数据<br>
                3. 可以上传佛像和香品图片到对应目录
            </div>
        </div>
        
        <div class="buttons">
            <a href="index.php" class="btn">进入网站</a>
            <a href="login.php" class="btn btn-secondary">管理员登录</a>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
